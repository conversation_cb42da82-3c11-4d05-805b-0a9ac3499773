# Contentlayer2 移除清单

本文档详细列出了从 ShipAny 项目中完全移除 contentlayer2 的所有步骤和文件。

## 📁 需要删除的文件和目录

### 1. 提供者实现文件
```
src/services/content/providers/contentlayer2/
├── provider.ts                    # 主要提供者实现
├── index.ts                      # 导出文件
└── types.ts                      # 类型定义（如果存在）
```

### 2. 配置文件
```
contentlayer.config.ts            # Contentlayer2 配置文件
```

### 3. 生成的文件目录
```
.contentlayer/                    # 整个目录及其内容
├── generated/
│   ├── index.mjs
│   ├── types.d.ts
│   └── 其他生成文件
└── 其他缓存文件
```

## 📦 需要移除的依赖

### package.json 依赖项
```json
{
  "dependencies": {
    "contentlayer2": "^0.4.6",           # 移除
    "next-contentlayer2": "^0.4.6"       # 移除
  }
}
```

### package.json 脚本命令
需要更新或移除的脚本：
```json
{
  "scripts": {
    "dev": "run-s build:content dev:parallel",           # 保留，但内部逻辑会变化
    "build": "run-s build:content generate:content next:build", # 保留，但内部逻辑会变化
    "build:content": "tsx scripts/build-content.ts"     # 保留，但移除contentlayer2逻辑
  }
}
```

## 🔧 需要更新的配置文件

### 1. next.config.mjs
移除的内容：
```javascript
// 移除 contentlayer2 默认值
const CONTENT_PROVIDER = process.env.CONTENT_PROVIDER || 'contentlayer2';

// 移除 contentlayer2 条件配置
if (CONTENT_PROVIDER === 'contentlayer2') {
  console.log('[Next.js Config] Applying withContentlayer');
  const { withContentlayer } = require("next-contentlayer2");
  config = withContentlayer(config);
}
```

更新为：
```javascript
// 更新默认值为 mdx
const CONTENT_PROVIDER = process.env.CONTENT_PROVIDER || 'mdx';

// 移除 contentlayer2 条件配置块
```

### 2. src/services/content/types/config.ts
更新默认配置：
```typescript
export const defaultConfig: ContentServiceConfig = {
  provider: (process.env.CONTENT_PROVIDER as ProviderType) || 'mdx', // 从 'contentlayer2' 改为 'mdx'
  // ... 其他配置保持不变
}
```

## 📜 需要更新的脚本文件

### 1. scripts/build-content.ts
移除的函数和逻辑：
```typescript
// 移除整个 buildContentlayer 函数
function buildContentlayer(watch: boolean) { ... }

// 更新 switch 语句，移除 contentlayer2 case
switch (provider) {
  case 'contentlayer2':        # 移除此 case
    buildContentlayer(isWatch)
    break
  // 保留其他 case
}
```

### 2. scripts/test-build-process.ts
移除所有 contentlayer2 相关测试：
```typescript
// 移除这些方法
private async testContentlayerBuild(): Promise<void> { ... }
private async testContentImports(): Promise<void> { ... }

// 更新 runAllTests 方法，移除相关调用
```

## 🧩 需要更新的组件文件

### 1. src/components/mdx/index.tsx
移除 contentlayer2 相关代码：
```typescript
// 移除条件导入
if (process.env.NEXT_PUBLIC_CONTENT_PROVIDER === 'contentlayer2') {
  try {
    const hooks = require('next-contentlayer2/hooks')
    useMDXComponent = hooks.useMDXComponent
  } catch (error) {
    console.warn('[MDX Component] Failed to load contentlayer2 hooks:', error)
  }
}

// 移除 useMDXComponent 相关逻辑
if (code) {
  if (!useMDXComponent) { ... }
  const Component = useMDXComponent(code)
  // ...
}
```

## 📚 需要更新的文档文件

### 1. docs/开发部署指南.md
移除或更新所有 contentlayer2 相关内容：
- 移除 "contentlayer build" 命令说明
- 更新工作流程说明
- 移除 .contentlayer 目录相关说明

### 2. docs/快速参考.md
更新所有工作流程：
- 移除 "pnpm contentlayer build" 命令
- 更新开发工作流
- 更新部署工作流

### 3. CLAUDE.md
更新架构说明：
- 移除 contentlayer2 作为默认提供者的说明
- 更新为 MDX 作为默认提供者

## 🔄 已完成的更新

### ✅ src/services/content/core/provider-selector.ts
- [x] 移除 contentlayer2 静态导入
- [x] 更新架构说明
- [x] 实现 MDX 优先的提供者选择
- [x] 添加回退机制

## 📋 执行顺序

### 阶段 1：移除静态导入和配置
1. ✅ 更新 provider-selector.ts
2. 🔄 更新 next.config.mjs
3. 🔄 更新 types/config.ts
4. 🔄 更新 components/mdx/index.tsx

### 阶段 2：删除文件和依赖
1. 🔄 删除 contentlayer2 提供者目录
2. 🔄 删除 contentlayer.config.ts
3. 🔄 清理 .contentlayer 目录
4. 🔄 更新 package.json

### 阶段 3：更新脚本和文档
1. 🔄 更新构建脚本
2. 🔄 更新测试脚本
3. 🔄 更新文档

### 阶段 4：测试和验证
1. 🔄 测试构建过程
2. 🔄 测试页面渲染
3. 🔄 测试组件功能

## ⚠️ 注意事项

1. **备份重要文件**：在删除前确保重要配置已备份
2. **依赖检查**：确保没有其他文件依赖被删除的模块
3. **测试覆盖**：每个阶段完成后进行测试
4. **回滚计划**：准备回滚方案以防出现问题

## 🎯 预期结果

完成所有步骤后：
- ✅ 项目不再依赖任何 contentlayer2 相关包
- ✅ MDX 成为唯一的内容提供者
- ✅ 构建过程不会尝试解析 contentlayer2/generated
- ✅ 所有页面和组件正常工作
- ✅ 为未来扩展其他提供者预留了架构空间

---

## 🎉 重构完成状态 - 2025年1月25日

### ✅ 所有阶段已完成

**阶段 1：配置文件更新** ✅
- [x] next.config.mjs - 默认提供者改为 'mdx'
- [x] src/services/content/types/config.ts - 移除contentlayer2类型，默认改为'mdx'
- [x] src/components/mdx/index.tsx - 移除contentlayer2条件导入

**阶段 2：代码文件移除** ✅
- [x] src/services/content/providers/contentlayer2/ - 完全删除
- [x] contentlayer.config.ts - 删除
- [x] .contentlayer/ - 清理目录

**阶段 3：依赖清理** ✅
- [x] package.json - 移除 contentlayer2 和 next-contentlayer2
- [x] src/services/content/core/provider-factory.ts - 移除contentlayer2引用

**阶段 4：构建脚本更新** ✅
- [x] scripts/build-content.ts - 移除contentlayer2构建逻辑
- [x] scripts/test-build-process.ts - 删除（专为contentlayer2设计）
- [x] scripts/generate-sitemap.ts - 更新为使用新的内容提供者API
- [x] scripts/generate-rss.ts - 更新为使用新的内容提供者API

**阶段 5：测试和验证** ✅
- [x] 生产构建成功 (pnpm build)
- [x] 开发服务器正常运行
- [x] 页面内容正确渲染（英文/中文）
- [x] VideoGallery组件正常工作
- [x] 语言切换功能正常

### 🚀 主要成就

1. **解决了核心问题**：
   - ❌ 静态导入依赖问题 → ✅ 完全移除contentlayer2
   - ❌ 英文页面显示中文内容 → ✅ 内容文件已修复
   - ❌ VideoGallery组件渲染问题 → ✅ MDX组件正常工作

2. **架构优化**：
   - 简化了内容提供者架构
   - MDX成为默认且唯一的提供者
   - 保持了未来扩展性

3. **构建优化**：
   - 构建时间减少（无需contentlayer2编译）
   - 包大小减小（移除不必要依赖）
   - 错误和警告消除

### 📊 技术指标

- **构建状态**: ✅ 成功
- **TypeScript检查**: ✅ 通过
- **依赖冲突**: ✅ 无
- **运行时错误**: ✅ 无
- **页面渲染**: ✅ 正常
- **组件功能**: ✅ 正常

**重构任务圆满完成！** 🎊
