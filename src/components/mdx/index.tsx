/**
 * MDX Rendering Component
 *
 * This component provides a consistent way to render MDX content with custom
 * styling and component mappings. It automatically detects the content format
 * and renders appropriately for different providers.
 * 
 * Design Philosophy:
 * - Provider-agnostic interface: Pages use <Mdx content={item} /> regardless of provider
 * - Format detection: Automatically handles different body formats (code vs raw)
 * - Performance preservation: Each provider's optimizations are maintained
 * - Extensibility: New providers can introduce new formats without breaking existing code
 * 
 * Supported Formats:
 * - body.raw: Raw MDX/Markdown from NextMDXRemote (runtime parsing)
 * - body.mdx: Raw MDX content from MDX provider
 * - Future: Can be extended for HTML, React components, etc.
 */

'use client'

import Image from 'next/image'
import { cn } from '@/lib/utils'
import { 
  Video, 
  YouTube, 
  Bilibili, 
  VideoGallery,
  type VideoProps,
  type YouTubeProps,
  type BilibiliProps,
  type VideoGalleryProps
} from './video-components'
import { Suspense, lazy } from 'react'

// Type definition for MDX component props
type MDXContentProps = {
  [props: string]: unknown
  components?: Record<string, React.ComponentType<Record<string, unknown>>>
}

// Type for HTML element props in MDX
type MDXElementProps = React.HTMLAttributes<HTMLElement> & {
  children?: React.ReactNode
  src?: string  // Add src property for img elements
  alt?: string  // Add alt property for img elements
}

// MDX component rendering is now handled by NextMDXRemote
// No need for contentlayer2 hooks

interface MdxProps {
  code?: string       // Legacy support for direct MDX code
  content?: {         // Content object with body for provider abstraction
    body?: {
      raw?: string    // NextMDXRemote format
      mdx?: string    // MDX provider format
    }
  }
  className?: string  // Optional CSS classes to apply to the container
}

// Lazy load the markdown renderer component only when needed
const MarkdownRenderer = lazy(() => import('./markdown-renderer'))

/**
 * Custom component mappings for MDX content
 *
 * These components replace default HTML elements in MDX with styled
 * versions that match our design system. Each component receives props
 * from the MDX renderer and applies consistent styling.
 */
const components = {
  // Replace img tags with Next.js optimized Image component
  img: (props: MDXElementProps) => {
    // Ensure src is provided, use a placeholder if not
    const src = props.src || '/placeholder.svg';
    
    return (
      <Image
        {...props}
        src={src}
        alt={props.alt || ''}
        width={800}
        height={400}
        className="rounded-lg border"
      />
    );
  },

  // Styled heading components with consistent spacing and typography
  h1: (props: MDXElementProps) => (
    <h1 className="mt-8 mb-4 text-3xl font-bold tracking-tight" {...props} />
  ),
  h2: (props: MDXElementProps) => (
    <h2 className="mt-6 mb-3 text-2xl font-semibold tracking-tight" {...props} />
  ),
  h3: (props: MDXElementProps) => (
    <h3 className="mt-4 mb-2 text-xl font-semibold tracking-tight" {...props} />
  ),

  // Paragraph with consistent spacing and muted text color
  p: (props: MDXElementProps) => (
    <p className="mb-4 leading-7 text-muted-foreground" {...props} />
  ),
  // List components with proper indentation and spacing
  ul: (props: MDXElementProps) => (
    <ul className="mb-4 ml-6 list-disc space-y-2" {...props} />
  ),
  ol: (props: MDXElementProps) => (
    <ol className="mb-4 ml-6 list-decimal space-y-2" {...props} />
  ),
  li: (props: MDXElementProps) => (
    <li className="leading-7 text-muted-foreground" {...props} />
  ),

  // Blockquote with left border accent
  blockquote: (props: MDXElementProps) => (
    <blockquote
      className="mb-4 border-l-4 border-primary pl-4 italic text-muted-foreground"
      {...props}
    />
  ),

  // Inline code with background highlight
  code: (props: MDXElementProps) => (
    <code
      className="rounded bg-muted px-1.5 py-0.5 text-sm font-mono"
      {...props}
    />
  ),

  // Code blocks with syntax highlighting background
  pre: (props: MDXElementProps) => (
    <pre
      className="mb-4 overflow-x-auto rounded-lg bg-muted p-4 text-sm"
      {...props}
    />
  ),
  // Links with external link styling and security attributes
  a: (props: MDXElementProps) => (
    <a
      className="text-primary underline underline-offset-4 hover:text-primary/80"
      target="_blank"
      rel="noopener noreferrer"
      {...props}
    />
  ),

  // Table components with responsive wrapper and consistent borders
  table: (props: MDXElementProps) => (
    <div className="mb-4 overflow-x-auto">
      <table className="w-full border-collapse border border-border" {...props} />
    </div>
  ),
  th: (props: MDXElementProps) => (
    <th className="border border-border bg-muted p-2 text-left font-semibold" {...props} />
  ),
  td: (props: MDXElementProps) => (
    <td className="border border-border p-2" {...props} />
  ),

  // Video components for multimedia content
  // Wrapper components to satisfy TypeScript's type requirements
  Video: (props: Record<string, unknown>) => <Video {...(props as unknown as VideoProps)} />,
  YouTube: (props: Record<string, unknown>) => <YouTube {...(props as unknown as YouTubeProps)} />,
  Bilibili: (props: Record<string, unknown>) => <Bilibili {...(props as unknown as BilibiliProps)} />,
  VideoGallery: (props: Record<string, unknown>) => <VideoGallery {...(props as unknown as VideoGalleryProps)} />,
}

/**
 * Main MDX rendering component
 *
 * Provides a unified interface for rendering MDX content from different providers.
 * This is the ONLY component pages need to use for rendering content, regardless
 * of which provider is active.
 *
 * @param code - Compiled MDX code from Contentlayer (legacy support)
 * @param content - Content object with body field (recommended, provider-agnostic)
 * @param className - Optional additional CSS classes
 * 
 * @example
 * // Recommended usage (provider-agnostic)
 * const blog = await getContent('blog', slug, locale)
 * <Mdx content={blog} />
 * 
 * @example
 * // Legacy usage (Contentlayer specific)
 * <Mdx code={blog.body.code} />
 */
export function Mdx({ code, content, className }: MdxProps) {
  // Determine the content format and render appropriately
  // This is where the "partial abstraction" happens - we detect
  // the format and use the appropriate renderer
  
  // Case 1: Direct code prop (legacy support)
  if (code) {
    return (
      <Suspense fallback={
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-4 bg-muted rounded w-2/3"></div>
        </div>
      }>
        <MarkdownRenderer
          source={code}
          components={components}
          className={className}
        />
      </Suspense>
    )
  }
  
  // Case 2: Content object with body
  if (content?.body) {
    
    // Raw markdown/MDX format (NextMDXRemote and similar providers)
    // These providers store raw content for runtime flexibility:
    // - Works in Cloudflare Workers (no build step needed)
    // - Allows dynamic content sources
    // - Trade-off: Runtime parsing cost
    if (content.body.raw) {
      return (
        <Suspense fallback={
          <div className={cn('prose prose-neutral dark:prose-invert max-w-none animate-pulse', className)}>
            <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        }>
          <MarkdownRenderer
            source={content.body.raw}
            components={components}
            className={className}
          />
        </Suspense>
      )
    }

    // MDX Provider format (platform-adaptive)
    // This provider adapts to the deployment environment:
    // - Cloudflare Workers: Uses precompiled static chunks
    // - Vercel/Node.js: Uses filesystem-based loading
    // - Trade-off: Runtime compilation for flexibility
    if ((content.body as any).mdx) {
      return (
        <Suspense fallback={
          <div className={cn('prose prose-neutral dark:prose-invert max-w-none animate-pulse', className)}>
            <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        }>
          <MarkdownRenderer
            source={(content.body as any).mdx}
            components={components}
            className={className}
          />
        </Suspense>
      )
    }
  }
  
  // Fallback: No valid content found
  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <p className="text-red-500">No valid MDX content found</p>
    </div>
  )
}
