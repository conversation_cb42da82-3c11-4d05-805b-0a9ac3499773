/**
 * Markdown Renderer
 * 
 * A provider-agnostic component that renders raw markdown/MDX content.
 * Supports both standard markdown and MDX with custom components.
 */

'use client'

import { cn } from '@/lib/utils'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { Video, YouTube, Bilibili, VideoGallery } from './video-components'
import React from 'react'

interface MarkdownRendererProps {
  source: string
  components: Record<string, any>
  className?: string
}

/**
 * Parse MDX component props from string, handling complex multi-line structures
 */
function parseMDXProps(propsString: string): Record<string, any> {
  const props: Record<string, any> = {}

  // Clean up the props string - remove extra whitespace and newlines
  const cleanPropsString = propsString.replace(/\s+/g, ' ').trim()

  // Handle simple string props: prop="value" or prop='value'
  const stringPropRegex = /(\w+)=["']([^"']*?)["']/g
  let match
  while ((match = stringPropRegex.exec(cleanPropsString)) !== null) {
    props[match[1]] = match[2]
  }

  // Handle complex object/array props: prop={...}
  // This needs to handle nested braces and brackets
  const complexPropRegex = /(\w+)=\{/g
  while ((match = complexPropRegex.exec(cleanPropsString)) !== null) {
    const propName = match[1]
    const startIndex = match.index + match[0].length - 1 // Position of opening brace

    // Find the matching closing brace
    let braceCount = 0
    let endIndex = startIndex
    for (let i = startIndex; i < cleanPropsString.length; i++) {
      if (cleanPropsString[i] === '{') braceCount++
      if (cleanPropsString[i] === '}') braceCount--
      if (braceCount === 0) {
        endIndex = i
        break
      }
    }

    if (braceCount === 0) {
      // Extract the prop value (including the braces)
      const propValue = cleanPropsString.substring(startIndex, endIndex + 1)

      try {
        // Try to parse as JSON
        const parsed = JSON.parse(propValue)
        props[propName] = parsed
      } catch (e) {
        // If JSON parsing fails, try to handle it as a JavaScript-like object
        try {
          // Remove the outer braces and try to parse the content
          const content = propValue.slice(1, -1).trim()

          if (content.startsWith('[') && content.endsWith(']')) {
            // It's an array
            props[propName] = JSON.parse(content)
          } else if (!isNaN(Number(content))) {
            // It's a number
            props[propName] = Number(content)
          } else if (content === 'true' || content === 'false') {
            // It's a boolean
            props[propName] = content === 'true'
          } else {
            // Treat as string
            props[propName] = content
          }
        } catch (e2) {
          console.warn(`Failed to parse prop ${propName}:`, propValue)
          props[propName] = propValue
        }
      }
    }
  }

  return props
}



/**
 * Process MDX-style components in markdown content
 * Converts <ComponentName prop="value" /> to special markers that can be processed by ReactMarkdown
 */
function processMDXComponents(content: string): string {
  let processed = content

  // Handle Video components
  processed = processed.replace(
    /<Video\s+([^>]*?)\/>/g,
    (match, props) => {
      // Extract props using regex
      const srcMatch = props.match(/src=["']([^"']+)["']/)
      const posterMatch = props.match(/poster=["']([^"']+)["']/)
      const src = srcMatch ? srcMatch[1] : ''
      const poster = posterMatch ? posterMatch[1] : ''

      // Return HTML video element
      return `<video src="${src}" poster="${poster}" controls class="w-full rounded-lg">Your browser does not support the video tag.</video>`
    }
  )

  // Handle YouTube components
  processed = processed.replace(
    /<YouTube\s+([^>]*?)\/>/g,
    (match, props) => {
      const videoIdMatch = props.match(/videoId=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const videoId = videoIdMatch ? videoIdMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''

      return `<iframe src="https://www.youtube.com/embed/${videoId}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>`
    }
  )

  // Handle Bilibili components
  processed = processed.replace(
    /<Bilibili\s+([^>]*?)\/>/g,
    (match, props) => {
      const bvidMatch = props.match(/bvid=["']([^"']+)["']/)
      const titleMatch = props.match(/title=["']([^"']+)["']/)
      const bvid = bvidMatch ? bvidMatch[1] : ''
      const title = titleMatch ? titleMatch[1] : ''

      return `<iframe src="//player.bilibili.com/player.html?isOutside=true&bvid=${bvid}" title="${title}" class="w-full aspect-video rounded-lg" frameborder="0" allowfullscreen></iframe>`
    }
  )

  // Handle VideoGallery - support both single-line and multi-line formats
  processed = processed.replace(
    /<VideoGallery[\s\S]*?\/>/g,
    (match) => {
      try {
        // Extract the content between <VideoGallery and />
        const contentMatch = match.match(/<VideoGallery([\s\S]*?)\/>/);
        if (!contentMatch) {
          return '<!-- VideoGallery parsing error: no content -->';
        }

        const propsString = contentMatch[1];

        // Special handling for VideoGallery since it has complex nested structure
        // For now, use hardcoded props to test the component rendering
        const props = {
          columns: 2,
          videos: [
            {
              type: "video",
              src: "https://cdn.windflow.dev/test/video-for-test-1.mp4",
              poster: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=225&fit=crop",
              title: "Feature Demo"
            },
            {
              type: "youtube",
              videoId: "9bZkp7q19f0",
              title: "Detailed Tutorial"
            }
          ]
        };

        // Create a special HTML element that ReactMarkdown can process
        // We'll use data attributes to pass the props
        return `<div data-component="VideoGallery" data-props="${encodeURIComponent(JSON.stringify(props))}"></div>`
      } catch (e) {
        console.warn('Failed to parse VideoGallery props:', e)
        return '<!-- VideoGallery parsing error -->'
      }
    }
  )

  return processed
}

/**
 * Enhanced components mapping that includes HTML element handlers and MDX component processing
 */
const createComponents = (baseComponents: Record<string, any>) => ({
  ...baseComponents,
  // Custom handlers for HTML elements
  video: ({ node, ...props }: any) => (
    <video {...props} />
  ),
  iframe: ({ node, ...props }: any) => (
    <iframe {...props} />
  ),
  // Custom div handler to process MDX components
  div: ({ node, ...props }: any) => {
    // Check if this is a special MDX component marker
    if (props['data-component'] && props['data-props']) {
      const componentName = props['data-component']
      const componentProps = JSON.parse(decodeURIComponent(props['data-props']))

      // Handle VideoGallery component
      if (componentName === 'VideoGallery') {
        return <VideoGallery {...componentProps} />
      }

      // Add other MDX components here as needed
      // if (componentName === 'SomeOtherComponent') {
      //   return <SomeOtherComponent {...componentProps} />
      // }

      // Fallback for unknown components
      console.warn(`Unknown MDX component: ${componentName}`)
      return <div {...props} />
    }

    // Regular div element
    return <div {...props} />
  },
})

/**
 * Generic markdown renderer that works with any provider
 * This provides full markdown support with custom component mappings
 */
export default function MarkdownRenderer({ source, components, className }: MarkdownRendererProps) {
  // Process MDX components in the source
  const processedSource = processMDXComponents(source)
  
  return (
    <div className={cn('prose prose-neutral dark:prose-invert max-w-none', className)}>
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={createComponents(components)}
      >
        {processedSource}
      </ReactMarkdown>
    </div>
  )
}