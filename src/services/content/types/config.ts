/**
 * Content Service Configuration Types
 * 
 * This module defines configuration interfaces for the content management service.
 * The configuration system provides setup options for different CMS providers,
 * feature flags, and deployment environments.
 */

import type { ContentType } from './content'

/**
 * Supported CMS Provider Types
 */
export type ProviderType =
  | 'mdx'               // Next.js built-in MDX support
  | 'next-mdx-remote'   // MDX with pre-compilation for edge environments
  | 'strapi'            // Headless CMS with REST/GraphQL API
  | 'sanity'            // Real-time headless CMS

/**
 * Content Service Features
 * 
 * Defines which optional features are enabled in the content service.
 */
export interface ContentFeatures {
  /** Enable SEO metadata generation */
  seo: boolean
  /** Enable related content suggestions */
  relatedContent: boolean
  /** Enable language switching functionality */
  languageSwitching: boolean
  /** Enable static content generation */
  staticGeneration: boolean
}

/**
 * Provider-specific Configuration
 * 
 * Configuration options specific to each CMS provider.
 */
export interface ProviderConfig {
  /** MDX-specific configuration */
  mdx?: {
    contentDir: string
    extensions: string[]
  }
  /** Next.js MDX Remote configuration */
  'next-mdx-remote'?: {
    contentDir?: string
    compiledDir?: string
  }
  /** Strapi-specific configuration */
  strapi?: {
    apiUrl: string
    apiToken?: string
  }
  /** Sanity-specific configuration */
  sanity?: {
    projectId: string
    dataset: string
    apiVersion: string
    token?: string
  }
  /** Allow any additional properties for flexibility */
  [key: string]: unknown
}

/**
 * Content Service Configuration
 * 
 * Main configuration interface for the content management service.
 */
export interface ContentServiceConfig {
  /** CMS provider to use */
  provider: ProviderType
  
  /** Provider-specific configuration */
  providerConfig?: ProviderConfig
  
  /** Array of content types to support */
  contentTypes: ContentType[]
  
  /** Default language locale */
  defaultLocale: string
  
  /** Array of supported language locales */
  supportedLocales: string[]
  
  /** Feature enablement configuration */
  features: ContentFeatures
  
  /** Base URL for the website (used for sitemap generation) */
  baseUrl?: string
  
  /** Whether to enable debug logging */
  debug?: boolean
}

/**
 * Default configuration values
 */
export const defaultConfig: ContentServiceConfig = {
  provider: (process.env.CONTENT_PROVIDER as ProviderType) || 'mdx',
  contentTypes: ['blog', 'product', 'case-study'],
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh'],
  features: {
    seo: true,
    relatedContent: true,
    languageSwitching: true,
    staticGeneration: true
  },
  debug: process.env.NODE_ENV === 'development'
}