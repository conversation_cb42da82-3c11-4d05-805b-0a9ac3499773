/**
 * MDX Compiler for Dynamic Content Processing
 * 
 * This module provides MDX compilation capabilities for the filesystem strategy,
 * allowing dynamic processing of MDX content at runtime.
 */

import matter from 'gray-matter'
import type { MDXContentItem } from '../types'
import type { ContentType } from '../../../types'

// Dynamic imports for Node.js modules to avoid client-side issues
let fs: any = null
let path: any = null

// Initialize Node.js modules only on server side
if (typeof window === 'undefined') {
  try {
    fs = require('fs-extra')
    path = require('path')
  } catch (error) {
    console.warn('[MDXCompiler] Node.js modules not available')
  }
}

/**
 * Whitelist of content directories to process
 * Must match the whitelist in build-mdx-provider.ts
 */
const CONTENT_TYPE_WHITELIST = [
  'blogs',
  'products',
  'case-studies'
]

/**
 * MDX Compiler class for processing MDX files
 */
export class MDXCompiler {
  private contentDir: string
  
  constructor(contentDir: string = './content') {
    this.contentDir = contentDir
  }
  
  /**
   * Compile MDX file to content item
   * 
   * @param filePath - Path to the MDX file
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @returns Compiled content item
   */
  async compileFile(
    filePath: string,
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<MDXContentItem | null> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return null
    }

    try {
      // Check if file exists
      if (!await fs.pathExists(filePath)) {
        console.warn(`[MDXCompiler] File not found: ${filePath}`)
        return null
      }
      
      // Read and parse the file
      const fileContent = await fs.readFile(filePath, 'utf-8')
      const { data, content } = matter(fileContent)
      
      // Get file stats for metadata
      const stats = await fs.stat(filePath)
      
      // Build content item with proper ContentItem structure
      const baseItem = {
        slug: data.slug || slug,
        title: data.title || this.generateTitleFromSlug(slug),
        lang: locale,
        url: `/${locale}/${this.getContentDirName(type)}/${slug}`,
        description: data.description || '',
        body: {
          mdx: content,
          // Component will be compiled at render time
          component: undefined,
          // HTML could be generated here if needed
          html: undefined
        },
        coverImage: data.coverImage || '',
        authorImage: data.authorImage || '',
        videoUrl: data.videoUrl || '',
        videoThumbnail: data.videoThumbnail || '',
        videoDuration: data.videoDuration || '',
        author: data.author || '',
        publishedAt: data.publishedAt || data.date || stats.birthtime.toISOString(),
        createdAt: data.createdAt || stats.birthtime.toISOString(),
        featured: data.featured || false,
        tags: data.tags || [],
        // Copy any additional frontmatter fields
        ...data
      }

      // Add type-specific properties
      let contentItem: MDXContentItem
      if (type === 'blog') {
        contentItem = { ...baseItem, type: 'blog' } as MDXContentItem
      } else if (type === 'product') {
        contentItem = { ...baseItem, type: 'product', icon: data.icon } as MDXContentItem
      } else if (type === 'case-study') {
        contentItem = { ...baseItem, type: 'case-study' } as MDXContentItem
      } else {
        console.warn(`[MDXCompiler] Unknown content type: ${type}`)
        return null
      }

      return contentItem
      
    } catch (error) {
      console.error(`[MDXCompiler] Failed to compile ${filePath}:`, error)
      return null
    }
  }
  
  /**
   * Load content from filesystem using content index
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @param fileMapping - File mapping from index
   * @returns Compiled content item
   */
  async loadFromIndex(
    type: ContentType,
    slug: string,
    locale: string,
    filePath: string
  ): Promise<MDXContentItem | null> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return null
    }

    const fullPath = path.resolve(this.contentDir, '..', filePath)
    return this.compileFile(fullPath, type, slug, locale)
  }
  
  /**
   * Load content directly from filesystem
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @returns Compiled content item
   */
  async loadDirect(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<MDXContentItem | null> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return null
    }

    // First try the direct file path approach (filename = slug)
    const dirName = this.getContentDirName(type)
    const directFilePath = path.join(this.contentDir, dirName, locale, `${slug}.mdx`)
    const fullDirectPath = path.resolve(directFilePath)

    if (await fs.pathExists(fullDirectPath)) {
      return this.compileFile(fullDirectPath, type, slug, locale)
    }

    // If direct approach fails, scan the directory to find file by slug in frontmatter
    return this.findFileBySlug(type, slug, locale)
  }

  /**
   * Find file by scanning directory and checking frontmatter slug
   */
  private async findFileBySlug(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<MDXContentItem | null> {
    try {
      const dirName = this.getContentDirName(type)
      const dirPath = path.join(this.contentDir, dirName, locale)
      const fullDirPath = path.resolve(dirPath)

      if (!await fs.pathExists(fullDirPath)) {
        return null
      }

      const files = await fs.readdir(fullDirPath)
      const mdxFiles = files.filter((file: string) => file.endsWith('.mdx'))

      // Check each file's frontmatter for matching slug
      for (const file of mdxFiles) {
        const filePath = path.join(fullDirPath, file)

        try {
          const fileContent = await fs.readFile(filePath, 'utf-8')
          const { data } = matter(fileContent)

          // Check if this file has the slug we're looking for
          if (data.slug === slug) {
            return this.compileFile(filePath, type, slug, locale)
          }
        } catch (error) {
          console.warn(`[MDXCompiler] Failed to read file ${filePath}:`, error)
          continue
        }
      }

      return null

    } catch (error) {
      console.error(`[MDXCompiler] Failed to find file by slug ${slug}:`, error)
      return null
    }
  }
  
  /**
   * Scan directory for content files
   * 
   * @param type - Content type
   * @param locale - Content locale
   * @returns Array of content items
   */
  async scanDirectory(
    type: ContentType,
    locale: string
  ): Promise<MDXContentItem[]> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return []
    }

    try {
      const dirName = this.getContentDirName(type)
      const dirPath = path.join(this.contentDir, dirName, locale)
      const fullDirPath = path.resolve(dirPath)
      
      // Check if directory exists
      if (!await fs.pathExists(fullDirPath)) {
        return []
      }
      
      // Read directory
      const files = await fs.readdir(fullDirPath)
      const mdxFiles = files.filter((file: string) => file.endsWith('.mdx'))
      
      // Compile all files
      const contentItems: MDXContentItem[] = []
      
      for (const file of mdxFiles) {
        const slug = path.basename(file, '.mdx')
        const filePath = path.join(fullDirPath, file)
        
        const content = await this.compileFile(filePath, type, slug, locale)
        if (content) {
          contentItems.push(content)
        }
      }
      
      return contentItems
      
    } catch (error) {
      console.error(`[MDXCompiler] Failed to scan directory ${type}/${locale}:`, error)
      return []
    }
  }
  
  /**
   * Get all content slugs for a type
   * 
   * @param type - Content type
   * @returns Array of slug/locale pairs
   */
  async getAllSlugs(type: ContentType): Promise<Array<{ locale: string; slug: string }>> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return []
    }

    try {
      const dirName = this.getContentDirName(type)
      const typeDir = path.join(this.contentDir, dirName)
      const fullTypeDir = path.resolve(typeDir)
      
      if (!await fs.pathExists(fullTypeDir)) {
        return []
      }
      
      const locales = await fs.readdir(fullTypeDir)
      const slugs: Array<{ locale: string; slug: string }> = []
      
      for (const locale of locales) {
        const localeDir = path.join(fullTypeDir, locale)
        const localeDirStats = await fs.stat(localeDir)
        
        if (localeDirStats.isDirectory()) {
          const files = await fs.readdir(localeDir)
          const mdxFiles = files.filter((file: string) => file.endsWith('.mdx'))
          
          for (const file of mdxFiles) {
            const slug = path.basename(file, '.mdx')
            slugs.push({ locale, slug })
          }
        }
      }
      
      return slugs
      
    } catch (error) {
      console.error(`[MDXCompiler] Failed to get slugs for ${type}:`, error)
      return []
    }
  }
  
  /**
   * Check if content file exists
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Content locale
   * @returns True if file exists
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    // Check if we're on the server side
    if (!fs || !path) {
      console.warn('[MDXCompiler] File system operations not available on client side')
      return false
    }

    const dirName = this.getContentDirName(type)
    const filePath = path.join(this.contentDir, dirName, locale, `${slug}.mdx`)
    const fullPath = path.resolve(filePath)

    return fs.pathExists(fullPath)
  }
  
  /**
   * Generate title from slug
   *
   * @param slug - Content slug
   * @returns Generated title
   */
  private generateTitleFromSlug(slug: string): string {
    return slug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  /**
   * Get the correct directory name for a content type
   *
   * @param type - Content type
   * @returns Directory name
   */
  private getContentDirName(type: ContentType): string {
    // Map content types to actual directory names
    const dirMap: Record<string, string> = {
      'blog': 'blogs',
      'product': 'products',
      'case-study': 'case-studies'
    }

    const dirName = dirMap[type] || type

    // Verify the directory is in our whitelist
    if (!CONTENT_TYPE_WHITELIST.includes(dirName)) {
      console.warn(`[MDXCompiler] Content type '${type}' (${dirName}) not in whitelist`)
      return dirName // Still return it, but log warning
    }

    return dirName
  }
}
