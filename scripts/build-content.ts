#!/usr/bin/env tsx

/**
 * Universal Content Build Script
 * 
 * This script automatically selects the appropriate content provider
 * based on the CONTENT_PROVIDER environment variable and runs
 * the corresponding build process.
 */

import { execSync } from 'child_process'
import path from 'path'
import dotenv from 'dotenv'

// Load environment variables from .env files
dotenv.config({ path: '.env.development' })
dotenv.config({ path: '.env.local' })
dotenv.config({ path: '.env' })

/**
 * Main build function
 */
async function buildContent() {
  const provider = process.env.CONTENT_PROVIDER || 'mdx'
  const isWatch = process.argv.includes('--watch') || process.argv.includes('-w')
  
  console.log(`[Content Build] Provider: ${provider}`)
  console.log(`[Content Build] Mode: ${isWatch ? 'watch' : 'build'}`)
  
  try {
    switch (provider) {
      case 'mdx':
        await buildMDXProvider(isWatch)
        break

      default:
        console.log(`[Content Build] Unknown provider: ${provider}`)
        console.log('[Content Build] No build required')
    }
  } catch (error) {
    console.error('[Content Build] Build failed:', error)
    process.exit(1)
  }
}





/**
 * Build content with MDX provider
 *
 * @param watch - Enable watch mode
 */
async function buildMDXProvider(watch: boolean) {
  console.log('[Content Build] Running MDX Provider build...')

  // Run the MDX provider build script
  const buildScriptPath = path.join(__dirname, 'build-mdx-provider.ts')
  const args = watch ? ['--watch'] : []

  execSync(`tsx ${buildScriptPath} ${args.join(' ')}`, {
    stdio: 'inherit',
    env: { ...process.env }
  })
}

// Run the build
buildContent().catch(error => {
  console.error('[Content Build] Fatal error:', error)
  process.exit(1)
})